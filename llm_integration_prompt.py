# Additional requirements.txt dependencies:
# openai>=1.0.0
# tiktoken>=0.5.0
# tenacity>=8.2.0
# jinja2>=3.1.0

import openai
import tiktoken
import json
import time
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from jinja2 import Template
import re
import hashlib

# Import from Phase 1
from db_con_schema import SchemaInfo, TableInfo, ColumnInfo

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# =============================================================================
# ENUMS AND CONSTANTS
# =============================================================================

class QueryIntent(str, Enum):
    SELECT = "select"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    CREATE = "create"
    DROP = "drop"
    ALTER = "alter"
    UNKNOWN = "unknown"

class PromptTemplate(str, Enum):
    BASE_SQL_GENERATION = "base_sql_generation"
    INTENT_CLASSIFICATION = "intent_classification"
    SCHEMA_SUMMARY = "schema_summary"
    FOLLOW_UP_QUERY = "follow_up_query"
    ERROR_RECOVERY = "error_recovery"

# Model configuration
DEFAULT_MODEL = "gpt-4"
MAX_TOKENS_GPT4 = 8192
MAX_TOKENS_GPT35 = 4096
CONTEXT_BUFFER = 500  # Reserve tokens for response

# =============================================================================
# DATA MODELS
# =============================================================================

@dataclass
class FewShotExample:
    question: str
    sql: str
    explanation: Optional[str] = None
    domain: Optional[str] = None  # e.g., "energy", "finance"
    difficulty: Optional[str] = "medium"  # easy, medium, hard
    intent: Optional[QueryIntent] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "question": self.question,
            "sql": self.sql,
            "explanation": self.explanation,
            "domain": self.domain,
            "difficulty": self.difficulty,
            "intent": self.intent.value if self.intent else None
        }

@dataclass
class LLMResponse:
    sql_query: str
    intent: QueryIntent
    confidence: float
    explanation: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    tokens_used: int = 0
    response_time: float = 0.0
    model_used: str = ""

@dataclass
class PromptContext:
    schema_info: SchemaInfo
    few_shot_examples: List[FewShotExample]
    user_query: str
    conversation_history: List[Dict[str, str]] = field(default_factory=list)
    domain_context: Optional[str] = None
    max_tokens: int = MAX_TOKENS_GPT4

# =============================================================================
# OPENAI CLIENT WITH RETRY LOGIC
# =============================================================================

class OpenAIClient:
    """OpenAI API client with error handling and retry logic"""
    
    def __init__(self, api_key: str, model: str = DEFAULT_MODEL, timeout: int = 30):
        self.client = openai.OpenAI(api_key=api_key, timeout=timeout)
        self.model = model
        self.encoding = tiktoken.encoding_for_model(model)
        
        # Rate limiting
        self.requests_per_minute = 3000
        self.tokens_per_minute = 90000
        self.request_timestamps = []
        self.token_usage = []
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.encoding.encode(text))
    
    def _check_rate_limits(self, estimated_tokens: int):
        """Check and enforce rate limits"""
        now = time.time()
        
        # Clean old timestamps (older than 1 minute)
        self.request_timestamps = [ts for ts in self.request_timestamps if now - ts < 60]
        self.token_usage = [(ts, tokens) for ts, tokens in self.token_usage if now - ts < 60]
        
        # Check request rate limit
        if len(self.request_timestamps) >= self.requests_per_minute:
            sleep_time = 60 - (now - self.request_timestamps[0])
            if sleep_time > 0:
                logger.warning(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
        
        # Check token rate limit
        total_tokens = sum(tokens for _, tokens in self.token_usage)
        if total_tokens + estimated_tokens >= self.tokens_per_minute:
            oldest_timestamp = self.token_usage[0][0] if self.token_usage else now
            sleep_time = 60 - (now - oldest_timestamp)
            if sleep_time > 0:
                logger.warning(f"Token rate limit reached, sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((openai.RateLimitError, openai.APITimeoutError))
    )
    def complete_chat(self, messages: List[Dict[str, str]], max_tokens: int = 1000, 
                     temperature: float = 0.1) -> Dict[str, Any]:
        """Make chat completion request with retry logic"""
        start_time = time.time()
        
        # Estimate tokens for rate limiting
        prompt_text = "\n".join([msg["content"] for msg in messages])
        estimated_tokens = self.count_tokens(prompt_text) + max_tokens
        
        # Check rate limits
        self._check_rate_limits(estimated_tokens)
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                response_format={"type": "json_object"}
            )
            
            # Track usage
            self.request_timestamps.append(time.time())
            if response.usage:
                self.token_usage.append((time.time(), response.usage.total_tokens))
            
            response_time = time.time() - start_time
            
            return {
                "content": response.choices[0].message.content,
                "tokens_used": response.usage.total_tokens if response.usage else 0,
                "response_time": response_time,
                "model": self.model
            }
            
        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in OpenAI request: {str(e)}")
            raise

# =============================================================================
# PROMPT TEMPLATE MANAGER
# =============================================================================

class PromptTemplateManager:
    """Manages prompt templates for different SQL generation tasks"""
    
    def __init__(self):
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, Template]:
        """Load and compile Jinja2 templates"""
        templates = {}
        
        # Base SQL Generation Template
        templates[PromptTemplate.BASE_SQL_GENERATION] = Template("""
You are an expert SQL query generator. Generate accurate, safe SQL queries based on natural language requests.

**Database Schema:**
{% for table in schema.tables %}
Table: {{ table.name }}
{% if table.description %}Description: {{ table.description }}{% endif %}
Columns:
{% for column in table.columns %}
  - {{ column.name }} ({{ column.type }}{% if not column.nullable %}, NOT NULL{% endif %}{% if column.primary_key %}, PRIMARY KEY{% endif %}{% if column.foreign_key %}, FK -> {{ column.foreign_key }}{% endif %})
{% if column.description %}    Description: {{ column.description }}{% endif %}
{% endfor %}

{% endfor %}

{% if relationships %}
**Relationships:**
{% for rel in relationships %}
- {{ rel.from }} -> {{ rel.to }}
{% endfor %}
{% endif %}

{% if examples %}
**Examples:**
{% for example in examples %}
Q: {{ example.question }}
SQL: {{ example.sql }}
{% if example.explanation %}Explanation: {{ example.explanation }}{% endif %}

{% endfor %}
{% endif %}

**Instructions:**
1. Generate ONLY valid SQL that matches the schema exactly
2. Use proper JOIN syntax for relationships
3. Include appropriate WHERE clauses for filtering
4. Use parameterized queries when possible
5. Do not include any schema modifications (CREATE, DROP, ALTER)
6. Ensure column and table names exist in the schema

**User Query:** {{ user_query }}

{% if conversation_history %}
**Previous Context:**
{% for msg in conversation_history[-3:] %}
{{ msg.role }}: {{ msg.content }}
{% endfor %}
{% endif %}

Respond in JSON format:
{
  "sql": "your generated SQL query",
  "intent": "select|insert|update|delete",
  "confidence": 0.95,
  "explanation": "brief explanation of the query",
  "warnings": ["any warnings or assumptions"]
}
""")

        # Intent Classification Template
        templates[PromptTemplate.INTENT_CLASSIFICATION] = Template("""
Classify the intent of this database query request.

**Query:** {{ user_query }}

**Classification Options:**
- SELECT: Retrieve/query data
- INSERT: Add new records
- UPDATE: Modify existing records  
- DELETE: Remove records
- CREATE: Create tables/views
- DROP: Delete tables/views
- ALTER: Modify table structure
- UNKNOWN: Cannot determine intent

**Examples:**
- "Show me sales data" -> SELECT
- "Add a new customer" -> INSERT
- "Update user email" -> UPDATE
- "Remove old records" -> DELETE
- "What's the total revenue?" -> SELECT

Respond in JSON format:
{
  "intent": "select",
  "confidence": 0.95,
  "reasoning": "User is requesting to retrieve data"
}
""")

        # Schema Summary Template (for large schemas)
        templates[PromptTemplate.SCHEMA_SUMMARY] = Template("""
Create a concise summary of the database schema focusing on tables most relevant to: "{{ user_query }}"

**Full Schema:**
{% for table in schema.tables %}
{{ table.name }}: {% for col in table.columns %}{{ col.name }}({{ col.type }}){% if not loop.last %}, {% endif %}{% endfor %}
{% endfor %}

**Relationships:**
{% for rel in relationships %}
{{ rel.from }} -> {{ rel.to }}
{% endfor %}

Select the 3-5 most relevant tables and provide a focused schema summary.

Respond in JSON format:
{
  "relevant_tables": ["table1", "table2"],
  "summary": "Focused schema description",
  "confidence": 0.9
}
""")

        # Follow-up Query Template
        templates[PromptTemplate.FOLLOW_UP_QUERY] = Template("""
This is a follow-up query in an ongoing conversation about database queries.

**Previous Query:** {{ previous_query }}
**Previous SQL:** {{ previous_sql }}
**Previous Results Summary:** {{ results_summary }}

**Current Query:** {{ user_query }}

**Schema Context:**
{% for table in relevant_tables %}
{{ table.name }}: {% for col in table.columns %}{{ col.name }}{% if not loop.last %}, {% endif %}{% endfor %}
{% endfor %}

Generate SQL that builds upon or modifies the previous query context.

Respond in JSON format:
{
  "sql": "your generated SQL query",
  "intent": "select",
  "confidence": 0.95,
  "explanation": "how this relates to the previous query",
  "is_followup": true
}
""")

        # Error Recovery Template
        templates[PromptTemplate.ERROR_RECOVERY] = Template("""
The previous SQL query failed with an error. Generate a corrected version.

**Original Query:** {{ original_query }}
**Failed SQL:** {{ failed_sql }}
**Error Message:** {{ error_message }}

**Schema Reference:**
{% for table in schema.tables %}
{{ table.name }}: {% for col in table.columns %}{{ col.name }}({{ col.type }}){% if not loop.last %}, {% endif %}{% endfor %}
{% endfor %}

**Common Issues to Check:**
- Column names and table names exist
- Proper JOIN syntax
- Data type compatibility
- WHERE clause syntax

Generate corrected SQL and explain the fix.

Respond in JSON format:
{
  "sql": "corrected SQL query",
  "intent": "select",
  "confidence": 0.85,
  "explanation": "what was fixed",
  "fixes_applied": ["list of corrections made"]
}
""")

        return templates
    
    def render_template(self, template_type: PromptTemplate, **kwargs) -> str:
        """Render a template with given context"""
        if template_type not in self.templates:
            raise ValueError(f"Template not found: {template_type}")
        
        return self.templates[template_type].render(**kwargs)

# =============================================================================
# FEW-SHOT EXAMPLE MANAGER
# =============================================================================

class FewShotExampleManager:
    """Manages few-shot examples for prompt engineering"""
    
    def __init__(self):
        self.examples: Dict[str, List[FewShotExample]] = {}
        self._load_default_examples()
    
    def _load_default_examples(self):
        """Load default few-shot examples"""
        energy_examples = [
            FewShotExample(
                question="Show total energy generation by plant for May 2025",
                sql="SELECT plant_id, SUM(power_kwh) as total_power FROM energy_metrics WHERE timestamp >= '2025-05-01' AND timestamp < '2025-06-01' GROUP BY plant_id",
                explanation="Aggregates power generation by plant for May 2025",
                domain="energy",
                intent=QueryIntent.SELECT
            ),
            FewShotExample(
                question="Find plants with capacity greater than 100 MW",
                sql="SELECT plant_id, location, capacity_mw FROM plant_info WHERE capacity_mw > 100",
                explanation="Filters plants by capacity threshold",
                domain="energy",
                intent=QueryIntent.SELECT
            ),
            FewShotExample(
                question="Add a new wind farm record",
                sql="INSERT INTO plant_info (plant_id, location, capacity_mw, owner) VALUES (?, ?, ?, ?)",
                explanation="Inserts new plant information with parameters",
                domain="energy",
                intent=QueryIntent.INSERT
            ),
            FewShotExample(
                question="Update plant capacity for PLANT001",
                sql="UPDATE plant_info SET capacity_mw = ? WHERE plant_id = 'PLANT001'",
                explanation="Updates specific plant capacity",
                domain="energy",
                intent=QueryIntent.UPDATE
            )
        ]
        
        self.examples["energy"] = energy_examples
        self.examples["general"] = [
            FewShotExample(
                question="Show all records from users table",
                sql="SELECT * FROM users",
                explanation="Simple select all query",
                intent=QueryIntent.SELECT
            ),
            FewShotExample(
                question="Count total number of orders",
                sql="SELECT COUNT(*) as total_orders FROM orders",
                explanation="Aggregate count query",
                intent=QueryIntent.SELECT
            )
        ]
    
    def add_example(self, domain: str, example: FewShotExample):
        """Add a new few-shot example"""
        if domain not in self.examples:
            self.examples[domain] = []
        self.examples[domain].append(example)
        logger.info(f"Added example to {domain} domain")
    
    def get_relevant_examples(self, domain: Optional[str] = None, 
                            intent: Optional[QueryIntent] = None,
                            max_examples: int = 3) -> List[FewShotExample]:
        """Get relevant examples based on domain and intent"""
        all_examples = []
        
        # Get domain-specific examples first
        if domain and domain in self.examples:
            all_examples.extend(self.examples[domain])
        
        # Add general examples
        all_examples.extend(self.examples.get("general", []))
        
        # Filter by intent if specified
        if intent:
            all_examples = [ex for ex in all_examples if ex.intent == intent]
        
        # Sort by relevance (domain match first, then by creation date)
        all_examples.sort(key=lambda x: (
            x.domain == domain if domain else False,
            x.created_at
        ), reverse=True)
        
        return all_examples[:max_examples]
    
    def export_examples(self, domain: Optional[str] = None) -> List[Dict[str, Any]]:
        """Export examples as JSON-serializable format"""
        if domain:
            return [ex.to_dict() for ex in self.examples.get(domain, [])]
        else:
            all_examples = []
            for domain_examples in self.examples.values():
                all_examples.extend([ex.to_dict() for ex in domain_examples])
            return all_examples
    
    def import_examples(self, examples_data: List[Dict[str, Any]], domain: str):
        """Import examples from JSON data"""
        for ex_data in examples_data:
            example = FewShotExample(
                question=ex_data["question"],
                sql=ex_data["sql"],
                explanation=ex_data.get("explanation"),
                domain=ex_data.get("domain", domain),
                difficulty=ex_data.get("difficulty", "medium"),
                intent=QueryIntent(ex_data["intent"]) if ex_data.get("intent") else None
            )
            self.add_example(domain, example)

# =============================================================================
# CONTEXT WINDOW MANAGER
# =============================================================================

class ContextWindowManager:
    """Manages context window for large schemas and conversations"""
    
    def __init__(self, model: str = DEFAULT_MODEL):
        self.model = model
        self.encoding = tiktoken.encoding_for_model(model)
        self.max_tokens = MAX_TOKENS_GPT4 if "gpt-4" in model else MAX_TOKENS_GPT35
        self.reserved_tokens = CONTEXT_BUFFER
        
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.encoding.encode(text))
    
    def summarize_schema(self, schema: SchemaInfo, user_query: str, 
                        max_tables: int = 10) -> SchemaInfo:
        """Summarize schema to fit context window"""
        # Score tables by relevance to user query
        table_scores = self._score_table_relevance(schema.tables, user_query)
        
        # Select top tables
        relevant_tables = [table for table, _ in table_scores[:max_tables]]
        
        # Filter relationships to only include relevant tables
        table_names = {table.name for table in relevant_tables}
        relevant_relationships = []
        for rel in schema.relationships:
            from_table = rel["from"].split(".")[0]
            to_table = rel["to"].split(".")[0]
            if from_table in table_names and to_table in table_names:
                relevant_relationships.append(rel)
        
        return SchemaInfo(
            database_name=schema.database_name,
            tables=relevant_tables,
            relationships=relevant_relationships,
            created_at=schema.created_at,
            version=schema.version
        )
    
    def _score_table_relevance(self, tables: List[TableInfo], user_query: str) -> List[Tuple[TableInfo, float]]:
        """Score tables by relevance to user query"""
        query_tokens = set(user_query.lower().split())
        scored_tables = []
        
        for table in tables:
            score = 0.0
            
            # Table name similarity
            table_tokens = set(table.name.lower().replace("_", " ").split())
            score += len(query_tokens.intersection(table_tokens)) * 2.0
            
            # Column name similarity
            for column in table.columns:
                col_tokens = set(column.name.lower().replace("_", " ").split())
                score += len(query_tokens.intersection(col_tokens)) * 1.0
            
            # Description similarity (if available)
            if table.description:
                desc_tokens = set(table.description.lower().split())
                score += len(query_tokens.intersection(desc_tokens)) * 1.5
            
            scored_tables.append((table, score))
        
        # Sort by score descending
        scored_tables.sort(key=lambda x: x[1], reverse=True)
        return scored_tables
    
    def truncate_conversation_history(self, history: List[Dict[str, str]], 
                                   max_history_tokens: int = 1000) -> List[Dict[str, str]]:
        """Truncate conversation history to fit within token limit"""
        if not history:
            return []
        
        # Start from most recent and work backwards
        truncated = []
        total_tokens = 0
        
        for msg in reversed(history):
            msg_tokens = self.count_tokens(f"{msg['role']}: {msg['content']}")
            if total_tokens + msg_tokens <= max_history_tokens:
                truncated.insert(0, msg)
                total_tokens += msg_tokens
            else:
                break
        
        return truncated
    
    def estimate_prompt_tokens(self, context: PromptContext) -> int:
        """Estimate total tokens for a prompt context"""
        # Schema tokens
        schema_text = json.dumps(context.schema_info.dict())
        schema_tokens = self.count_tokens(schema_text)
        
        # Examples tokens
        examples_text = "\n".join([
            f"Q: {ex.question}\nSQL: {ex.sql}" 
            for ex in context.few_shot_examples
        ])
        examples_tokens = self.count_tokens(examples_text)
        
        # Query tokens
        query_tokens = self.count_tokens(context.user_query)
        
        # History tokens
        history_text = "\n".join([
            f"{msg['role']}: {msg['content']}" 
            for msg in context.conversation_history
        ])
        history_tokens = self.count_tokens(history_text)
        
        # Base template tokens (approximate)
        template_tokens = 500
        
        return schema_tokens + examples_tokens + query_tokens + history_tokens + template_tokens
    
    def optimize_context(self, context: PromptContext) -> PromptContext:
        """Optimize context to fit within token limits"""
        available_tokens = self.max_tokens - self.reserved_tokens
        current_tokens = self.estimate_prompt_tokens(context)
        
        if current_tokens <= available_tokens:
            return context
        
        logger.info(f"Context too large ({current_tokens} tokens), optimizing...")
        
        # Step 1: Summarize schema if needed
        if len(context.schema_info.tables) > 10:
            context.schema_info = self.summarize_schema(
                context.schema_info, 
                context.user_query,
                max_tables=min(8, len(context.schema_info.tables))
            )
        
        # Step 2: Reduce examples
        if len(context.few_shot_examples) > 2:
            context.few_shot_examples = context.few_shot_examples[:2]
        
        # Step 3: Truncate conversation history
        if context.conversation_history:
            context.conversation_history = self.truncate_conversation_history(
                context.conversation_history,
                max_history_tokens=800
            )
        
        # Final check
        final_tokens = self.estimate_prompt_tokens(context)
        logger.info(f"Context optimized to {final_tokens} tokens")
        
        return context

# =============================================================================
# QUERY INTENT CLASSIFIER
# =============================================================================

class QueryIntentClassifier:
    """Classifies user query intent (SELECT, INSERT, UPDATE, DELETE, etc.)"""
    
    def __init__(self, openai_client: OpenAIClient, template_manager: PromptTemplateManager):
        self.openai_client = openai_client
        self.template_manager = template_manager
        self._intent_keywords = {
            QueryIntent.SELECT: [
                "show", "display", "list", "get", "find", "search", "query", 
                "select", "retrieve", "fetch", "what", "which", "how many", 
                "count", "sum", "average", "total", "report"
            ],
            QueryIntent.INSERT: [
                "add", "insert", "create", "new", "register", "save", 
                "store", "enter", "record"
            ],
            QueryIntent.UPDATE: [
                "update", "modify", "change", "edit", "set", "alter", 
                "correct", "fix", "revise"
            ],
            QueryIntent.DELETE: [
                "delete", "remove", "drop", "clear", "erase", "eliminate"
            ]
        }
    
    def classify_intent_fast(self, user_query: str) -> Tuple[QueryIntent, float]:
        """Fast keyword-based intent classification"""
        query_lower = user_query.lower()
        scores = {intent: 0.0 for intent in QueryIntent}
        
        for intent, keywords in self._intent_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    scores[intent] += 1.0
        
        # Get the highest scoring intent
        best_intent = max(scores.items(), key=lambda x: x[1])
        
        if best_intent[1] > 0:
            confidence = min(0.8, best_intent[1] * 0.3)  # Cap at 0.8 for keyword-based
            return best_intent[0], confidence
        else:
            return QueryIntent.UNKNOWN, 0.1
    
    def classify_intent_llm(self, user_query: str) -> Tuple[QueryIntent, float]:
        """LLM-based intent classification for complex queries"""
        try:
            prompt = self.template_manager.render_template(
                PromptTemplate.INTENT_CLASSIFICATION,
                user_query=user_query
            )
            
            messages = [{"role": "user", "content": prompt}]
            response = self.openai_client.complete_chat(messages, max_tokens=200)
            
            result = json.loads(response["content"])
            intent = QueryIntent(result.get("intent", "unknown"))
            confidence = float(result.get("confidence", 0.5))
            
            return intent, confidence
            
        except Exception as e:
            logger.error(f"LLM intent classification failed: {str(e)}")
            return self.classify_intent_fast(user_query)
    
    def classify_intent(self, user_query: str, use_llm: bool = True) -> Tuple[QueryIntent, float]:
        """Classify query intent with fallback strategy"""
        # Try fast classification first
        fast_intent, fast_confidence = self.classify_intent_fast(user_query)
        
        # Use LLM for ambiguous cases or when explicitly requested
        if use_llm and (fast_confidence < 0.6 or fast_intent == QueryIntent.UNKNOWN):
            return self.classify_intent_llm(user_query)
        else:
            return fast_intent, fast_confidence

# =============================================================================
# MAIN LLM INTEGRATION MANAGER
# =============================================================================

class LLMIntegrationManager:
    """Main manager for LLM integration and SQL generation"""
    
    def __init__(self, openai_api_key: str, model: str = DEFAULT_MODEL):
        self.openai_client = OpenAIClient(openai_api_key, model)
        self.template_manager = PromptTemplateManager()
        self.example_manager = FewShotExampleManager()
        self.context_manager = ContextWindowManager(model)
        self.intent_classifier = QueryIntentClassifier(self.openai_client, self.template_manager)
        
        # Conversation tracking
        self.conversations: Dict[str, List[Dict[str, str]]] = {}
    
    def generate_sql(self, user_query: str, schema_info: SchemaInfo, 
                    domain: Optional[str] = None, 
                    conversation_id: Optional[str] = None,
                    use_examples: bool = True) -> LLMResponse:
        """Generate SQL from natural language query"""
        try:
            start_time = time.time()
            
            # Classify intent
            intent, intent_confidence = self.intent_classifier.classify_intent(user_query)
            
            # Get relevant examples
            examples = []
            if use_examples:
                examples = self.example_manager.get_relevant_examples(
                    domain=domain, 
                    intent=intent,
                    max_examples=3
                )
            
            # Get conversation history
            history = self.conversations.get(conversation_id, []) if conversation_id else []
            
            # Build context
            context = PromptContext(
                schema_info=schema_info,
                few_shot_examples=examples,
                user_query=user_query,
                conversation_history=history,
                domain_context=domain
            )
            
            # Optimize context for token limits
            context = self.context_manager.optimize_context(context)
            
            # Generate prompt
            prompt = self.template_manager.render_template(
                PromptTemplate.BASE_SQL_GENERATION,
                schema=context.schema_info,
                examples=context.few_shot_examples,
                user_query=user_query,
                conversation_history=context.conversation_history
            )
            
            # Make LLM request
            messages = [{"role": "user", "content": prompt}]
            response = self.openai_client.complete_chat(
                messages, 
                max_tokens=1000,
                temperature=0.1
            )
            
            # Parse response
            result = json.loads(response["content"])
            
            # Create LLM response object
            llm_response = LLMResponse(
                sql_query=result.get("sql", ""),
                intent=QueryIntent(result.get("intent", intent.value)),
                confidence=float(result.get("confidence", intent_confidence)),
                explanation=result.get("explanation"),
                warnings=result.get("warnings", []),
                tokens_used=response["tokens_used"],
                response_time=time.time() - start_time,
                model_used=response["model"]
            )
            
            # Update conversation history
            if conversation_id:
                self._update_conversation(conversation_id, user_query, llm_response.sql_query)
            
            return llm_response
            
        except Exception as e:
            logger.error(f"SQL generation failed: {str(e)}")
            return LLMResponse(
                sql_query="",
                intent=QueryIntent.UNKNOWN,
                confidence=0.0,
                explanation=f"Error: {str(e)}",
                warnings=["SQL generation failed"],
                response_time=time.time() - start_time if 'start' in locals() else 0.0,
                model_used=self.openai_client.model,
                tokens_used=0


            )