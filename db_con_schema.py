# requirements.txt dependencies:
# sqlalchemy>=2.0.0
# redis>=4.5.0
# pydantic>=2.0.0
# PyYAML>=6.0
# psycopg2-binary>=2.9.0  # for PostgreSQL
# snowflake-sqlalchemy>=1.4.0  # for Snowflake
# pymysql>=1.0.0  # for MySQL

import json
import yaml
import redis
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import logging
from sqlalchemy import (
    create_engine, MetaData, Table, Column, 
    inspect, text, Integer, String, Float, 
    Boolean, DateTime, Date, Time
)
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel, Field, validator
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# =============================================================================
# ENUMS AND CONSTANTS
# =============================================================================

class DatabaseType(str, Enum):
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    SNOWFLAKE = "snowflake"
    SQLITE = "sqlite"
    MSSQL = "mssql"

class ColumnType(str, Enum):
    INTEGER = "integer"
    STRING = "string"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATETIME = "datetime"
    DATE = "date"
    TIME = "time"
    TEXT = "text"
    JSON = "json"

# =============================================================================
# PYDANTIC MODELS FOR SCHEMA DEFINITIONS
# =============================================================================

class ColumnInfo(BaseModel):
    name: str
    type: ColumnType
    nullable: bool = True
    primary_key: bool = False
    foreign_key: Optional[str] = None  # format: "table.column"
    default_value: Optional[Any] = None
    description: Optional[str] = None
    
    class Config:
        use_enum_values = True

class TableInfo(BaseModel):
    name: str
    columns: List[ColumnInfo]
    description: Optional[str] = None
    
    @validator('columns')
    def validate_columns(cls, v):
        if not v:
            raise ValueError("Table must have at least one column")
        return v

class SchemaInfo(BaseModel):
    database_name: str
    tables: List[TableInfo]
    relationships: List[Dict[str, str]] = []  # [{"from": "table1.col", "to": "table2.col"}]
    created_at: datetime = Field(default_factory=datetime.now)
    version: str = "1.0"
    
    @validator('tables')
    def validate_tables(cls, v):
        if not v:
            raise ValueError("Schema must have at least one table")
        return v

class ManualSchemaInput(BaseModel):
    database_name: str
    tables: Dict[str, Dict[str, Any]]  # Flexible input format
    relationships: Optional[List[Dict[str, str]]] = []
    examples: Optional[List[Dict[str, str]]] = []  # Q&A examples

# =============================================================================
# DATABASE CONNECTION MANAGER
# =============================================================================

@dataclass
class DatabaseConfig:
    db_type: DatabaseType
    host: str
    port: int
    database: str
    username: str
    password: str
    schema: Optional[str] = None
    additional_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_params is None:
            self.additional_params = {}

class DatabaseConnectionManager:
    """Manages database connections for multiple database types"""
    
    def __init__(self):
        self._engines: Dict[str, Engine] = {}
        self._connection_strings = {
            DatabaseType.POSTGRESQL: "postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}",
            DatabaseType.MYSQL: "mysql+pymysql://{username}:{password}@{host}:{port}/{database}",
            DatabaseType.SNOWFLAKE: "snowflake://{username}:{password}@{host}/{database}?warehouse={warehouse}&role={role}",
            DatabaseType.SQLITE: "sqlite:///{database}",
            DatabaseType.MSSQL: "mssql+pyodbc://{username}:{password}@{host}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server"
        }
    
    def create_connection(self, config: DatabaseConfig) -> str:
        """Create a database connection and return connection ID"""
        connection_id = self._generate_connection_id(config)
        
        try:
            connection_string = self._build_connection_string(config)
            engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
            
            # Test connection
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self._engines[connection_id] = engine
            logger.info(f"Successfully created connection: {connection_id}")
            return connection_id
            
        except Exception as e:
            logger.error(f"Failed to create connection: {str(e)}")
            raise ConnectionError(f"Database connection failed: {str(e)}")
    
    def get_engine(self, connection_id: str) -> Engine:
        """Get SQLAlchemy engine by connection ID"""
        if connection_id not in self._engines:
            raise ValueError(f"Connection not found: {connection_id}")
        return self._engines[connection_id]
    
    def close_connection(self, connection_id: str):
        """Close a database connection"""
        if connection_id in self._engines:
            self._engines[connection_id].dispose()
            del self._engines[connection_id]
            logger.info(f"Closed connection: {connection_id}")
    
    def _build_connection_string(self, config: DatabaseConfig) -> str:
        """Build connection string based on database type"""
        template = self._connection_strings.get(config.db_type)
        if not template:
            raise ValueError(f"Unsupported database type: {config.db_type}")
        
        params = {
            'username': config.username,
            'password': config.password,
            'host': config.host,
            'port': config.port,
            'database': config.database
        }
        
        # Add additional parameters for specific databases
        if config.db_type == DatabaseType.SNOWFLAKE:
            params.update({
                'warehouse': config.additional_params.get('warehouse', 'COMPUTE_WH'),
                'role': config.additional_params.get('role', 'PUBLIC')
            })
        
        return template.format(**params)
    
    def _generate_connection_id(self, config: DatabaseConfig) -> str:
        """Generate unique connection ID"""
        config_str = f"{config.db_type}_{config.host}_{config.port}_{config.database}_{config.username}"
        return hashlib.md5(config_str.encode()).hexdigest()[:16]

# =============================================================================
# SCHEMA INTROSPECTION MODULE
# =============================================================================

class SchemaIntrospector:
    """Automatically discovers database schema through introspection"""
    
    def __init__(self, connection_manager: DatabaseConnectionManager):
        self.connection_manager = connection_manager
        self._type_mapping = {
            'INTEGER': ColumnType.INTEGER,
            'VARCHAR': ColumnType.STRING,
            'TEXT': ColumnType.TEXT,
            'FLOAT': ColumnType.FLOAT,
            'DOUBLE': ColumnType.FLOAT,
            'BOOLEAN': ColumnType.BOOLEAN,
            'DATETIME': ColumnType.DATETIME,
            'DATE': ColumnType.DATE,
            'TIME': ColumnType.TIME,
            'JSON': ColumnType.JSON,
        }
    
    def introspect_schema(self, connection_id: str, schema_name: Optional[str] = None) -> SchemaInfo:
        """Introspect database schema and return structured information"""
        try:
            engine = self.connection_manager.get_engine(connection_id)
            inspector = inspect(engine)
            
            # Get table names
            table_names = inspector.get_table_names(schema=schema_name)
            if not table_names:
                raise ValueError("No tables found in the specified schema")
            
            tables = []
            relationships = []
            
            for table_name in table_names:
                table_info = self._introspect_table(inspector, table_name, schema_name)
                tables.append(table_info)
                
                # Get foreign key relationships
                fks = inspector.get_foreign_keys(table_name, schema=schema_name)
                for fk in fks:
                    for col_pair in zip(fk['constrained_columns'], fk['referred_columns']):
                        relationships.append({
                            'from': f"{table_name}.{col_pair[0]}",
                            'to': f"{fk['referred_table']}.{col_pair[1]}"
                        })
            
            return SchemaInfo(
                database_name=schema_name or "default",
                tables=tables,
                relationships=relationships
            )
            
        except Exception as e:
            logger.error(f"Schema introspection failed: {str(e)}")
            raise RuntimeError(f"Failed to introspect schema: {str(e)}")
    
    def _introspect_table(self, inspector, table_name: str, schema_name: Optional[str]) -> TableInfo:
        """Introspect a single table"""
        columns = []
        
        # Get column information
        col_info = inspector.get_columns(table_name, schema=schema_name)
        pk_info = inspector.get_pk_constraint(table_name, schema=schema_name)
        fk_info = inspector.get_foreign_keys(table_name, schema=schema_name)
        
        # Create foreign key mapping
        fk_mapping = {}
        for fk in fk_info:
            for i, col in enumerate(fk['constrained_columns']):
                fk_mapping[col] = f"{fk['referred_table']}.{fk['referred_columns'][i]}"
        
        for col in col_info:
            column_type = self._map_sql_type_to_column_type(str(col['type']))
            
            columns.append(ColumnInfo(
                name=col['name'],
                type=column_type,
                nullable=col['nullable'],
                primary_key=col['name'] in pk_info.get('constrained_columns', []),
                foreign_key=fk_mapping.get(col['name']),
                default_value=col.get('default')
            ))
        
        return TableInfo(name=table_name, columns=columns)
    
    def _map_sql_type_to_column_type(self, sql_type: str) -> ColumnType:
        """Map SQL types to our ColumnType enum"""
        sql_type_upper = sql_type.upper()
        
        for sql_key, col_type in self._type_mapping.items():
            if sql_key in sql_type_upper:
                return col_type
        
        # Default fallback
        if any(keyword in sql_type_upper for keyword in ['CHAR', 'TEXT']):
            return ColumnType.STRING
        elif any(keyword in sql_type_upper for keyword in ['INT', 'SERIAL']):
            return ColumnType.INTEGER
        elif any(keyword in sql_type_upper for keyword in ['FLOAT', 'DOUBLE', 'DECIMAL', 'NUMERIC']):
            return ColumnType.FLOAT
        else:
            return ColumnType.STRING

# =============================================================================
# SCHEMA STORAGE SYSTEM
# =============================================================================

class SchemaStorageManager:
    """Manages schema storage with Redis caching and in-memory fallback"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_client = None
        self.memory_cache: Dict[str, Dict] = {}
        self.cache_ttl = 3600  # 1 hour default TTL
        
        if redis_url:
            try:
                self.redis_client = redis.from_url(redis_url)
                self.redis_client.ping()  # Test connection
                logger.info("Redis connection established")
            except Exception as e:
                logger.warning(f"Redis connection failed, using memory cache: {str(e)}")
                self.redis_client = None
    
    def store_schema(self, connection_id: str, schema_info: SchemaInfo) -> bool:
        """Store schema information with connection ID as key"""
        try:
            schema_key = f"schema:{connection_id}"
            schema_data = {
                'schema': schema_info.dict(),
                'timestamp': datetime.now().isoformat(),
                'ttl': self.cache_ttl
            }
            
            if self.redis_client:
                self.redis_client.setex(
                    schema_key,
                    self.cache_ttl,
                    json.dumps(schema_data, default=str)
                )
            else:
                self.memory_cache[schema_key] = schema_data
            
            logger.info(f"Schema stored for connection: {connection_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store schema: {str(e)}")
            return False
    
    def get_schema(self, connection_id: str) -> Optional[SchemaInfo]:
        """Retrieve schema information by connection ID"""
        try:
            schema_key = f"schema:{connection_id}"
            
            if self.redis_client:
                cached_data = self.redis_client.get(schema_key)
                if cached_data:
                    data = json.loads(cached_data)
                    return SchemaInfo(**data['schema'])
            else:
                if schema_key in self.memory_cache:
                    data = self.memory_cache[schema_key]
                    # Check if expired
                    stored_time = datetime.fromisoformat(data['timestamp'])
                    if datetime.now() - stored_time < timedelta(seconds=data['ttl']):
                        return SchemaInfo(**data['schema'])
                    else:
                        del self.memory_cache[schema_key]
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve schema: {str(e)}")
            return None
    
    def invalidate_schema(self, connection_id: str) -> bool:
        """Remove schema from cache"""
        try:
            schema_key = f"schema:{connection_id}"
            
            if self.redis_client:
                self.redis_client.delete(schema_key)
            else:
                self.memory_cache.pop(schema_key, None)
            
            logger.info(f"Schema invalidated for connection: {connection_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate schema: {str(e)}")
            return False
    
    def list_cached_schemas(self) -> List[str]:
        """List all cached schema connection IDs"""
        try:
            if self.redis_client:
                keys = self.redis_client.keys("schema:*")
                return [key.decode().replace("schema:", "") for key in keys]
            else:
                return [key.replace("schema:", "") for key in self.memory_cache.keys()]
        except Exception as e:
            logger.error(f"Failed to list schemas: {str(e)}")
            return []

# =============================================================================
# MANUAL SCHEMA INPUT & VALIDATION
# =============================================================================

class SchemaValidator:
    """Validates and normalizes schema definitions"""
    
    @staticmethod
    def validate_manual_schema(schema_input: Union[Dict, str, ManualSchemaInput]) -> SchemaInfo:
        """Validate and convert manual schema input to SchemaInfo"""
        try:
            # Handle different input formats
            if isinstance(schema_input, str):
                # Try to parse as JSON or YAML
                try:
                    data = json.loads(schema_input)
                except json.JSONDecodeError:
                    data = yaml.safe_load(schema_input)
            elif isinstance(schema_input, dict):
                data = schema_input
            elif isinstance(schema_input, ManualSchemaInput):
                data = schema_input.dict()
            else:
                raise ValueError("Invalid schema input format")
            
            # Validate required fields
            if 'database_name' not in data:
                raise ValueError("database_name is required")
            if 'tables' not in data or not data['tables']:
                raise ValueError("At least one table is required")
            
            # Convert to standardized format
            tables = []
            for table_name, table_def in data['tables'].items():
                columns = []
                
                if isinstance(table_def, dict) and 'columns' in table_def:
                    for col_name, col_def in table_def['columns'].items():
                        if isinstance(col_def, str):
                            # Simple format: "column_name": "type"
                            columns.append(ColumnInfo(
                                name=col_name,
                                type=ColumnType(col_def.lower())
                            ))
                        elif isinstance(col_def, dict):
                            # Detailed format
                            columns.append(ColumnInfo(
                                name=col_name,
                                type=ColumnType(col_def.get('type', 'string').lower()),
                                nullable=col_def.get('nullable', True),
                                primary_key=col_def.get('primary_key', False),
                                foreign_key=col_def.get('foreign_key'),
                                description=col_def.get('description')
                            ))
                
                tables.append(TableInfo(
                    name=table_name,
                    columns=columns,
                    description=table_def.get('description')
                ))
            
            return SchemaInfo(
                database_name=data['database_name'],
                tables=tables,
                relationships=data.get('relationships', [])
            )
            
        except Exception as e:
            logger.error(f"Schema validation failed: {str(e)}")
            raise ValueError(f"Invalid schema format: {str(e)}")
    
    @staticmethod
    def normalize_schema(schema_info: SchemaInfo) -> SchemaInfo:
        """Normalize and clean schema information"""
        # Remove duplicate tables
        seen_tables = set()
        unique_tables = []
        
        for table in schema_info.tables:
            if table.name not in seen_tables:
                seen_tables.add(table.name)
                
                # Remove duplicate columns
                seen_columns = set()
                unique_columns = []
                for column in table.columns:
                    if column.name not in seen_columns:
                        seen_columns.add(column.name)
                        unique_columns.append(column)
                
                table.columns = unique_columns
                unique_tables.append(table)
        
        schema_info.tables = unique_tables
        return schema_info

# =============================================================================
# MAIN SCHEMA MANAGER
# =============================================================================

class SchemaManager:
    """Main interface for schema management operations"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.connection_manager = DatabaseConnectionManager()
        self.introspector = SchemaIntrospector(self.connection_manager)
        self.storage = SchemaStorageManager(redis_url)
        self.validator = SchemaValidator()
    
    def connect_database(self, config: DatabaseConfig) -> str:
        """Connect to database and return connection ID"""
        return self.connection_manager.create_connection(config)
    
    def auto_discover_schema(self, connection_id: str, schema_name: Optional[str] = None, 
                           cache: bool = True) -> SchemaInfo:
        """Auto-discover schema through introspection"""
        schema_info = self.introspector.introspect_schema(connection_id, schema_name)
        
        if cache:
            self.storage.store_schema(connection_id, schema_info)
        
        return schema_info
    
    def manual_schema_input(self, connection_id: str, schema_input: Union[Dict, str], 
                          cache: bool = True) -> SchemaInfo:
        """Process manual schema input"""
        schema_info = self.validator.validate_manual_schema(schema_input)
        schema_info = self.validator.normalize_schema(schema_info)
        
        if cache:
            self.storage.store_schema(connection_id, schema_info)
        
        return schema_info
    
    def get_cached_schema(self, connection_id: str) -> Optional[SchemaInfo]:
        """Get cached schema information"""
        return self.storage.get_schema(connection_id)
    
    def refresh_schema(self, connection_id: str, schema_name: Optional[str] = None) -> SchemaInfo:
        """Refresh schema by re-introspecting and updating cache"""
        # Invalidate old cache
        self.storage.invalidate_schema(connection_id)
        
        # Re-introspect
        return self.auto_discover_schema(connection_id, schema_name, cache=True)
    
    def export_schema(self, connection_id: str, format: str = 'json') -> str:
        """Export schema to JSON or YAML format"""
        schema_info = self.get_cached_schema(connection_id)
        if not schema_info:
            raise ValueError(f"No schema found for connection: {connection_id}")
        
        schema_dict = schema_info.dict()
        
        if format.lower() == 'yaml':
            return yaml.dump(schema_dict, default_flow_style=False)
        else:
            return json.dumps(schema_dict, indent=2, default=str)
    
    def disconnect_database(self, connection_id: str):
        """Disconnect from database and clean up"""
        self.connection_manager.close_connection(connection_id)
        self.storage.invalidate_schema(connection_id)

# =============================================================================
# EXAMPLE USAGE AND TESTING
# =============================================================================

if __name__ == "__main__":
    # Example usage
    schema_manager = SchemaManager()
    
    # Example 1: PostgreSQL connection and auto-discovery
    try:
        pg_config = DatabaseConfig(
            db_type=DatabaseType.POSTGRESQL,
            host="localhost",
            port=5432,
            database="energy_db",
            username="user",
            password="password"
        )
        
        connection_id = schema_manager.connect_database(pg_config)
        schema = schema_manager.auto_discover_schema(connection_id)
        print(f"Discovered {len(schema.tables)} tables")
        
    except Exception as e:
        print(f"Database connection failed: {e}")
    
    # Example 2: Manual schema input
    manual_schema = {
        "database_name": "energy_metrics",
        "tables": {
            "energy_metrics": {
                "columns": {
                    "plant_id": {"type": "string", "primary_key": True},
                    "timestamp": {"type": "datetime"},
                    "power_kwh": {"type": "float"},
                    "temperature_c": {"type": "float"}
                },
                "description": "Energy generation metrics by plant"
            },
            "plant_info": {
                "columns": {
                    "plant_id": {"type": "string", "primary_key": True},
                    "location": {"type": "string"},
                    "capacity_mw": {"type": "float"},
                    "owner": {"type": "string"}
                }
            }
        },
        "relationships": [
            {"from": "energy_metrics.plant_id", "to": "plant_info.plant_id"}
        ]
    }
    
    try:
        manual_connection_id = "manual_001"
        schema = schema_manager.manual_schema_input(manual_connection_id, manual_schema)
        print(f"Manual schema processed: {schema.database_name}")
        
        # Export schema
        exported = schema_manager.export_schema(manual_connection_id, 'json')
        print("Exported schema:", exported[:200] + "...")
        
    except Exception as e:
        print(f"Manual schema processing failed: {e}")